# Password Storage Implementation

This document describes the password storage functionality implemented using IntelliJ's PasswordSafe API.

## Overview

The implementation provides secure password storage capabilities using IntelliJ Platform's built-in credential storage system. The PasswordSafe API automatically handles platform-specific secure storage:

- **Windows**: KeePass format file
- **macOS**: Keychain using Security Framework  
- **Linux**: Secret Service API using libsecret

## Files Added/Modified

### 1. PasswordStorageService.kt
**Location**: `src/main/kotlin/com/github/emprider/intellijplugin/services/PasswordStorageService.kt`

A project-level service that provides methods for:
- Storing passwords securely
- Retrieving stored credentials
- Removing stored passwords
- Demo/test functionality

### 2. MyToolWindowFactory.kt (Enhanced)
**Location**: `src/main/kotlin/com/github/emprider/intellijplugin/toolWindow/MyToolWindowFactory.kt`

Enhanced tool window with:
- Input fields for key, username, and password
- Buttons for store/retrieve/remove operations
- Result display area with timestamps
- Test functionality with demo data

### 3. MyBundle.properties (Updated)
**Location**: `src/main/resources/messages/MyBundle.properties`

Added message keys for the password storage UI elements.

### 4. PasswordStorageServiceTest.kt
**Location**: `src/test/kotlin/com/github/emprider/intellijplugin/PasswordStorageServiceTest.kt`

Comprehensive test suite covering all password storage operations.

## Key API Components

### CredentialAttributes
```kotlin
private fun createCredentialAttributes(key: String): CredentialAttributes {
    val serviceName = "$SERVICE_NAME.$key"
    return CredentialAttributes(serviceName)
}
```

### Storing Credentials
```kotlin
fun storePassword(key: String, username: String, password: String): Boolean {
    val attributes = createCredentialAttributes(key)
    val credentials = Credentials(username, password)
    PasswordSafe.instance.set(attributes, credentials)
    return true
}
```

### Retrieving Credentials
```kotlin
fun retrieveCredentials(key: String): Pair<String, String>? {
    val attributes = createCredentialAttributes(key)
    val credentials = PasswordSafe.instance.get(attributes)
    return if (credentials != null) {
        Pair(credentials.userName ?: "", credentials.getPasswordAsString() ?: "")
    } else null
}
```

## Usage Examples

### Basic Usage
```kotlin
// Get the service
val passwordService = project.service<PasswordStorageService>()

// Store a password
passwordService.storePassword("api-key", "<EMAIL>", "secret123")

// Retrieve credentials
val credentials = passwordService.retrieveCredentials("api-key")
credentials?.let { (username, password) ->
    println("Username: $username, Password: $password")
}

// Remove password
passwordService.removePassword("api-key")
```

### Test Functionality
```kotlin
// Store test password
passwordService.storeTestPassword()

// Check if test credentials exist
if (passwordService.hasTestCredentials()) {
    val testCredentials = passwordService.retrieveTestCredentials()
    // Use test credentials
}

// Clean up
passwordService.removeTestPassword()
```

## UI Components

The enhanced tool window provides:

1. **Input Section**:
   - Key field (required)
   - Username field (optional)
   - Password field (required for storage)

2. **Action Buttons**:
   - **Store Password**: Saves the entered credentials
   - **Retrieve Password**: Gets stored credentials for the key
   - **Remove Password**: Deletes stored credentials
   - **Test with Demo Data**: Quick test with predefined data
   - **Clear Results**: Clears the result display

3. **Result Display**:
   - Scrollable text area showing operation results
   - Timestamps for each operation
   - Status label showing current operation status

## Error Handling

The implementation includes comprehensive error handling:

- **Input Validation**: Checks for empty required fields
- **Exception Handling**: Try-catch blocks around all PasswordSafe operations
- **Logging**: Detailed logging for debugging and monitoring
- **User Feedback**: Clear status messages and error reporting

## Security Features

- **No Plain Text Storage**: Passwords are never stored in plain text
- **OS-Level Security**: Leverages operating system's secure storage mechanisms
- **User Control**: Users can configure storage method in IDE settings
- **Automatic Encryption**: Platform handles encryption/decryption transparently

## Testing

Run the test suite to verify functionality:

```bash
./gradlew test --tests "PasswordStorageServiceTest"
```

The tests cover:
- Basic store/retrieve operations
- Password-only retrieval
- Password removal
- Non-existent key handling
- Test password functionality
- Multiple password management
- Edge cases (empty username, etc.)

## Configuration

Users can configure password storage behavior in:
**Settings | Appearance & Behavior | System Settings | Passwords**

## Troubleshooting

### Common Issues

1. **Gradle Build Errors**: Ensure correct IntelliJ Platform version and dependencies
2. **Import Errors**: Verify PasswordSafe API imports are correct for your platform version
3. **Storage Issues**: Check OS-specific credential storage is properly configured

### Debug Information

Enable debug logging to see detailed operation information:
- Check IDE logs for PasswordStorageService messages
- Use the test functionality to verify basic operations
- Check the result display in the tool window for operation feedback

## Future Enhancements

Potential improvements:
- Password generation functionality
- Import/export capabilities
- Bulk operations
- Password strength validation
- Expiration date support
- Category/grouping support

## Dependencies

The implementation relies on:
- IntelliJ Platform SDK
- PasswordSafe API (`com.intellij.ide.passwordSafe.PasswordSafe`)
- Credential Store API (`com.intellij.credentialStore.*`)
- Standard Kotlin/Java libraries
