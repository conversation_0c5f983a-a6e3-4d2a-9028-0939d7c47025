# Password Storage Demo Plugin - User Guide

## What is this plugin?

This plugin demonstrates how to securely store and retrieve passwords in IntelliJ IDEA using the built-in PasswordSafe API. It's perfect for developers who need to store API keys, database passwords, or other sensitive credentials securely.

## Installation

1. **Download** the plugin ZIP file
2. **Open IntelliJ IDEA**
3. **Go to Settings/Preferences** → **Plugins**
4. **Click the gear icon** ⚙️ → **Install Plugin from Disk...**
5. **Select the ZIP file** you downloaded
6. **Restart IntelliJ IDEA**

## How to Use

### Finding the Tool Window

After installation, look for the **"Password Storage Demo"** tool window:
- **View** → **Tool Windows** → **Password Storage Demo**
- Or look for a tab on the right side of your IDE

### Quick Start - Test with Demo Data

1. **Open the tool window**
2. **Click "Test with Demo Data"** button
3. **Watch the results** appear in the text area below
4. This stores and retrieves test credentials automatically

### Manual Usage

#### Storing a Password:
1. **Enter a Key**: Something like `"github-token"` or `"database-password"`
2. **Enter Username**: Your username (optional)
3. **Enter Password**: Your actual password
4. **Click "Store Password"**
5. **See success message** in the results area

#### Retrieving a Password:
1. **Enter the Key** you used when storing
2. **Click "Retrieve Password"**
3. **See your credentials** displayed in the results area
4. **The username and password fields** will be automatically filled

#### Removing a Password:
1. **Enter the Key** you want to remove
2. **Click "Remove Password"**
3. **See confirmation** in the results area

### Security Features

✅ **Secure Storage**: Uses your operating system's secure credential storage
- **Windows**: Windows Credential Manager
- **macOS**: Keychain
- **Linux**: Secret Service (GNOME Keyring, KWallet)

✅ **No Plain Text**: Passwords are never stored in plain text files

✅ **User Control**: You can configure storage settings in IDE preferences

## Use Cases

### For Developers:
- **API Keys**: Store GitHub tokens, OpenAI API keys, etc.
- **Database Credentials**: Store database usernames and passwords
- **Server Access**: Store SSH credentials or server passwords
- **Service Tokens**: Store authentication tokens for various services

### Example Keys You Might Use:
- `github-personal-token`
- `openai-api-key`
- `production-database`
- `staging-server`
- `docker-registry`

## Tips

1. **Use Descriptive Keys**: Make your keys meaningful like `"prod-db-password"` instead of just `"password"`

2. **Test First**: Always use the "Test with Demo Data" button to make sure everything works

3. **Clear Results**: Use the "Clear Results" button to clean up the display area

4. **Check Status**: Watch the status line at the bottom for operation feedback

## Troubleshooting

### Plugin Not Visible:
- Check **View** → **Tool Windows** menu
- Try restarting IntelliJ IDEA
- Make sure the plugin is enabled in Settings → Plugins

### Storage Issues:
- Check your OS credential storage is working
- Try the test functionality first
- Look at the IDE logs for error messages

### Can't Store Passwords:
- Make sure you enter a key (required)
- Password field cannot be empty for storage
- Check the status messages for specific errors

## For Plugin Developers

This plugin serves as a complete example of:
- Using IntelliJ's PasswordSafe API
- Creating tool windows with Swing components
- Implementing secure credential storage
- Error handling and user feedback
- Writing comprehensive tests

The source code demonstrates best practices for IntelliJ plugin development.

## Support

If you encounter issues:
1. Check the IDE logs (Help → Show Log in Explorer/Finder)
2. Try the test functionality first
3. Make sure your OS credential storage is working
4. Contact the plugin developer

## Version Information

- **Version**: 1.0.0
- **Compatible with**: IntelliJ IDEA 2024.3+
- **Platforms**: Windows, macOS, Linux

---

**Happy secure coding!** 🔐
