[versions]
# libraries
junit = "4.13.2"
opentest4j = "1.3.0"

# plugins
changelog = "2.3.0"
intelliJPlatform = "2.7.0"
kotlin = "2.2.0"
kover = "0.9.1"
qodana = "2025.1.1"

[libraries]
junit = { group = "junit", name = "junit", version.ref = "junit" }
opentest4j = { group = "org.opentest4j", name = "opentest4j", version.ref = "opentest4j" }

[plugins]
changelog = { id = "org.jetbrains.changelog", version.ref = "changelog" }
intelliJPlatform = { id = "org.jetbrains.intellij.platform", version.ref = "intelliJPlatform" }
kotlin = { id = "org.jetbrains.kotlin.jvm", version.ref = "kotlin" }
kover = { id = "org.jetbrains.kotlinx.kover", version.ref = "kover" }
qodana = { id = "org.jetbrains.qodana", version.ref = "qodana" }
