package com.github.emprider.intellijplugin.services

import com.intellij.credentialStore.CredentialAttributes
import com.intellij.credentialStore.Credentials
import com.intellij.ide.passwordSafe.PasswordSafe
import com.intellij.openapi.components.Service
import com.intellij.openapi.diagnostic.thisLogger
import com.intellij.openapi.project.Project

/**
 * Service for managing password storage using IntelliJ's PasswordSafe API.
 * 
 * This service demonstrates how to securely store and retrieve sensitive data
 * using IntelliJ Platform's built-in credential storage system. The PasswordSafe
 * API automatically handles platform-specific secure storage:
 * - Windows: KeePass format file
 * - macOS: Keychain using Security Framework
 * - Linux: Secret Service API using libsecret
 * 
 * Users can configure the storage method in:
 * Settings | Appearance & Behavior | System Settings | Passwords
 */
@Service(Service.Level.PROJECT)
class PasswordStorageService(private val project: Project) {

    companion object {
        // Service name used for generating credential attributes
        // This should be unique to your plugin to avoid conflicts
        private const val SERVICE_NAME = "IntelliJPluginDemo"
        
        // Default test credentials for demonstration
        private const val TEST_KEY = "test-password"
        private const val TEST_USERNAME = "demo-user"
        private const val TEST_PASSWORD = "demo-password-123"
    }

    private val logger = thisLogger()

    /**
     * Creates credential attributes for a given key.
     * CredentialAttributes are used to uniquely identify stored credentials.
     *
     * @param key Unique identifier for the credential
     * @return CredentialAttributes object for the given key
     */
    private fun createCredentialAttributes(key: String): CredentialAttributes {
        // Create a unique service name by combining our service name with the key
        val serviceName = "$SERVICE_NAME.$key"
        return CredentialAttributes(serviceName)
    }

    /**
     * Stores a password securely using IntelliJ's PasswordSafe.
     * 
     * @param key Unique identifier for the credential
     * @param username Username associated with the credential
     * @param password Password to store securely
     * @return true if storage was successful, false otherwise
     */
    fun storePassword(key: String, username: String, password: String): Boolean {
        return try {
            val attributes = createCredentialAttributes(key)
            val credentials = Credentials(username, password)
            
            // Store the credentials using PasswordSafe
            PasswordSafe.instance.set(attributes, credentials)
            
            logger.info("Successfully stored password for key: $key")
            true
        } catch (e: Exception) {
            logger.error("Failed to store password for key: $key", e)
            false
        }
    }

    /**
     * Retrieves stored credentials for a given key.
     * 
     * @param key Unique identifier for the credential
     * @return Pair of (username, password) if found, null otherwise
     */
    fun retrieveCredentials(key: String): Pair<String, String>? {
        return try {
            val attributes = createCredentialAttributes(key)
            val credentials = PasswordSafe.instance.get(attributes)
            
            if (credentials != null) {
                val username = credentials.userName ?: ""
                val password = credentials.getPasswordAsString() ?: ""
                logger.info("Successfully retrieved credentials for key: $key")
                Pair(username, password)
            } else {
                logger.info("No credentials found for key: $key")
                null
            }
        } catch (e: Exception) {
            logger.error("Failed to retrieve credentials for key: $key", e)
            null
        }
    }

    /**
     * Retrieves only the password for a given key (without username).
     * 
     * @param key Unique identifier for the credential
     * @return Password string if found, null otherwise
     */
    fun retrievePassword(key: String): String? {
        return try {
            val attributes = createCredentialAttributes(key)
            val password = PasswordSafe.instance.getPassword(attributes)
            
            if (password != null) {
                logger.info("Successfully retrieved password for key: $key")
                password
            } else {
                logger.info("No password found for key: $key")
                null
            }
        } catch (e: Exception) {
            logger.error("Failed to retrieve password for key: $key", e)
            null
        }
    }

    /**
     * Removes stored credentials for a given key.
     * 
     * @param key Unique identifier for the credential to remove
     * @return true if removal was successful, false otherwise
     */
    fun removePassword(key: String): Boolean {
        return try {
            val attributes = createCredentialAttributes(key)
            
            // Remove credentials by setting them to null
            PasswordSafe.instance.set(attributes, null)
            
            logger.info("Successfully removed password for key: $key")
            true
        } catch (e: Exception) {
            logger.error("Failed to remove password for key: $key", e)
            false
        }
    }

    /**
     * Stores a test password for demonstration purposes.
     * This method can be used to quickly test the password storage functionality.
     * 
     * @return true if test password was stored successfully
     */
    fun storeTestPassword(): Boolean {
        logger.info("Storing test password for demonstration")
        return storePassword(TEST_KEY, TEST_USERNAME, TEST_PASSWORD)
    }

    /**
     * Retrieves the test password for demonstration purposes.
     * 
     * @return Test credentials if found, null otherwise
     */
    fun retrieveTestCredentials(): Pair<String, String>? {
        logger.info("Retrieving test password for demonstration")
        return retrieveCredentials(TEST_KEY)
    }

    /**
     * Removes the test password.
     * 
     * @return true if test password was removed successfully
     */
    fun removeTestPassword(): Boolean {
        logger.info("Removing test password")
        return removePassword(TEST_KEY)
    }

    /**
     * Checks if test credentials exist.
     * 
     * @return true if test credentials are stored, false otherwise
     */
    fun hasTestCredentials(): Boolean {
        return retrieveTestCredentials() != null
    }
}
