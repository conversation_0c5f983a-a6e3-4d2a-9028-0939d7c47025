package com.github.emprider.intellijplugin.toolWindow

import com.intellij.openapi.components.service
import com.intellij.openapi.diagnostic.thisLogger
import com.intellij.openapi.project.Project
import com.intellij.openapi.wm.ToolWindow
import com.intellij.openapi.wm.ToolWindowFactory
import com.intellij.ui.components.JBLabel
import com.intellij.ui.components.JBPanel
import com.intellij.ui.components.JBPasswordField
import com.intellij.ui.components.JBTextField
import com.intellij.ui.content.ContentFactory
import com.github.emprider.intellijplugin.MyBundle
import com.github.emprider.intellijplugin.services.MyProjectService
import com.github.emprider.intellijplugin.services.PasswordStorageService
import java.awt.BorderLayout
import java.awt.GridBagConstraints
import java.awt.GridBagLayout
import java.awt.Insets
import javax.swing.*


class MyToolWindowFactory : ToolWindowFactory {

    init {
        thisLogger().warn("Don't forget to remove all non-needed sample code files with their corresponding registration entries in `plugin.xml`.")
    }

    override fun createToolWindowContent(project: Project, toolWindow: ToolWindow) {
        val myToolWindow = MyToolWindow(toolWindow)
        val content = ContentFactory.getInstance().createContent(myToolWindow.getContent(), null, false)
        toolWindow.contentManager.addContent(content)
    }

    override fun shouldBeAvailable(project: Project) = true

    class MyToolWindow(toolWindow: ToolWindow) {

        private val service = toolWindow.project.service<MyProjectService>()
        private val passwordService = toolWindow.project.service<PasswordStorageService>()

        fun getContent() = JBPanel<JBPanel<*>>().apply {
            layout = BorderLayout()

            // Create the original random number section
            val originalSection = createOriginalSection()

            // Create the password storage section
            val passwordSection = createPasswordStorageSection()

            // Add both sections to the main panel
            add(originalSection, BorderLayout.NORTH)
            add(passwordSection, BorderLayout.CENTER)
        }

        private fun createOriginalSection(): JPanel {
            return JBPanel<JBPanel<*>>().apply {
                layout = BoxLayout(this, BoxLayout.Y_AXIS)
                border = BorderFactory.createTitledBorder("Random Number Demo")

                val label = JBLabel(MyBundle.message("randomLabel", "?"))
                val button = JButton(MyBundle.message("shuffle")).apply {
                    addActionListener {
                        label.text = MyBundle.message("randomLabel", service.getRandomNumber())
                    }
                }

                add(label)
                add(Box.createVerticalStrut(5))
                add(button)
                add(Box.createVerticalStrut(10))
            }
        }

        private fun createPasswordStorageSection(): JPanel {
            return JBPanel<JBPanel<*>>().apply {
                layout = BorderLayout()
                border = BorderFactory.createTitledBorder(MyBundle.message("passwordDemo.title"))

                // Create input panel
                val inputPanel = createInputPanel()

                // Create button panel
                val buttonPanel = createButtonPanel()

                // Create result panel
                val resultPanel = createResultPanel()

                add(inputPanel, BorderLayout.NORTH)
                add(buttonPanel, BorderLayout.CENTER)
                add(resultPanel, BorderLayout.SOUTH)
            }
        }

        // UI Components for password storage
        private val keyField = JBTextField(20)
        private val usernameField = JBTextField(20)
        private val passwordField = JBPasswordField().apply {
            columns = 20
        }
        private val resultArea = JTextArea(5, 30).apply {
            isEditable = false
            background = UIManager.getColor("Panel.background")
        }
        private val statusLabel = JBLabel(MyBundle.message("passwordDemo.statusLabel"))

        private fun createInputPanel(): JPanel {
            return JBPanel<JBPanel<*>>().apply {
                layout = GridBagLayout()
                val gbc = GridBagConstraints()

                // Key field
                gbc.gridx = 0; gbc.gridy = 0
                gbc.anchor = GridBagConstraints.WEST
                gbc.insets = Insets(5, 5, 5, 5)
                add(JBLabel(MyBundle.message("passwordDemo.keyLabel")), gbc)

                gbc.gridx = 1; gbc.fill = GridBagConstraints.HORIZONTAL
                gbc.weightx = 1.0
                add(keyField, gbc)

                // Username field
                gbc.gridx = 0; gbc.gridy = 1
                gbc.fill = GridBagConstraints.NONE
                gbc.weightx = 0.0
                add(JBLabel(MyBundle.message("passwordDemo.usernameLabel")), gbc)

                gbc.gridx = 1; gbc.fill = GridBagConstraints.HORIZONTAL
                gbc.weightx = 1.0
                add(usernameField, gbc)

                // Password field
                gbc.gridx = 0; gbc.gridy = 2
                gbc.fill = GridBagConstraints.NONE
                gbc.weightx = 0.0
                add(JBLabel(MyBundle.message("passwordDemo.passwordLabel")), gbc)

                gbc.gridx = 1; gbc.fill = GridBagConstraints.HORIZONTAL
                gbc.weightx = 1.0
                add(passwordField, gbc)
            }
        }

        private fun createButtonPanel(): JPanel {
            return JBPanel<JBPanel<*>>().apply {
                layout = GridBagLayout()
                val gbc = GridBagConstraints()
                gbc.insets = Insets(5, 5, 5, 5)

                // Store button
                gbc.gridx = 0; gbc.gridy = 0
                add(JButton(MyBundle.message("passwordDemo.storeButton")).apply {
                    addActionListener { storePassword() }
                }, gbc)

                // Retrieve button
                gbc.gridx = 1
                add(JButton(MyBundle.message("passwordDemo.retrieveButton")).apply {
                    addActionListener { retrievePassword() }
                }, gbc)

                // Remove button
                gbc.gridx = 2
                add(JButton(MyBundle.message("passwordDemo.removeButton")).apply {
                    addActionListener { removePassword() }
                }, gbc)

                // Test button (second row)
                gbc.gridx = 0; gbc.gridy = 1
                add(JButton(MyBundle.message("passwordDemo.testButton")).apply {
                    addActionListener { testWithDemoData() }
                }, gbc)

                // Clear button
                gbc.gridx = 1
                add(JButton(MyBundle.message("passwordDemo.clearButton")).apply {
                    addActionListener { clearResults() }
                }, gbc)
            }
        }

        private fun createResultPanel(): JPanel {
            return JBPanel<JBPanel<*>>().apply {
                layout = BorderLayout()
                border = BorderFactory.createTitledBorder(MyBundle.message("passwordDemo.resultLabel"))

                add(JScrollPane(resultArea), BorderLayout.CENTER)
                add(statusLabel, BorderLayout.SOUTH)
            }
        }

        // Action methods for password operations
        private fun storePassword() {
            val key = keyField.text.trim()
            val username = usernameField.text.trim()
            val password = String(passwordField.password)

            if (key.isEmpty()) {
                updateStatus(MyBundle.message("passwordDemo.error", "Key cannot be empty"))
                return
            }

            if (password.isEmpty()) {
                updateStatus(MyBundle.message("passwordDemo.error", "Password cannot be empty"))
                return
            }

            val success = passwordService.storePassword(key, username, password)
            if (success) {
                updateStatus(MyBundle.message("passwordDemo.stored"))
                appendResult("Stored password for key: '$key' with username: '$username'")
            } else {
                updateStatus(MyBundle.message("passwordDemo.failed"))
                appendResult("Failed to store password for key: '$key'")
            }
        }

        private fun retrievePassword() {
            val key = keyField.text.trim()

            if (key.isEmpty()) {
                updateStatus(MyBundle.message("passwordDemo.error", "Key cannot be empty"))
                return
            }

            val credentials = passwordService.retrieveCredentials(key)
            if (credentials != null) {
                val (username, password) = credentials
                updateStatus(MyBundle.message("passwordDemo.success"))
                appendResult("Retrieved credentials for key: '$key'")
                appendResult("Username: '$username'")
                appendResult("Password: '$password'")

                // Optionally populate the fields with retrieved data
                usernameField.text = username
                passwordField.text = password
            } else {
                updateStatus(MyBundle.message("passwordDemo.notFound"))
                appendResult("No credentials found for key: '$key'")
            }
        }

        private fun removePassword() {
            val key = keyField.text.trim()

            if (key.isEmpty()) {
                updateStatus(MyBundle.message("passwordDemo.error", "Key cannot be empty"))
                return
            }

            val success = passwordService.removePassword(key)
            if (success) {
                updateStatus(MyBundle.message("passwordDemo.removed"))
                appendResult("Removed password for key: '$key'")
            } else {
                updateStatus(MyBundle.message("passwordDemo.failed"))
                appendResult("Failed to remove password for key: '$key'")
            }
        }

        private fun testWithDemoData() {
            // Store test password
            val success = passwordService.storeTestPassword()
            if (success) {
                updateStatus(MyBundle.message("passwordDemo.success"))
                appendResult("Test password stored successfully")

                // Retrieve and display test credentials
                val credentials = passwordService.retrieveTestCredentials()
                if (credentials != null) {
                    val (username, password) = credentials
                    appendResult("Test credentials retrieved:")
                    appendResult("Username: '$username'")
                    appendResult("Password: '$password'")

                    // Populate fields with test data
                    keyField.text = "test-password"
                    usernameField.text = username
                    passwordField.text = password
                }
            } else {
                updateStatus(MyBundle.message("passwordDemo.failed"))
                appendResult("Failed to store test password")
            }
        }

        private fun clearResults() {
            resultArea.text = ""
            statusLabel.text = MyBundle.message("passwordDemo.statusLabel")
        }

        private fun updateStatus(message: String) {
            statusLabel.text = "${MyBundle.message("passwordDemo.statusLabel")} $message"
        }

        private fun appendResult(message: String) {
            if (resultArea.text.isNotEmpty()) {
                resultArea.append("\n")
            }
            resultArea.append("${java.time.LocalTime.now()}: $message")
            resultArea.caretPosition = resultArea.document.length
        }
    }
}
