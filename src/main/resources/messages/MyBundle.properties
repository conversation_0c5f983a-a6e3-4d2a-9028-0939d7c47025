projectService=Project service: {0}
randomLabel=The random number is: {0}
shuffle=Shuffle

# Password Storage Demo
passwordDemo.title=Password Storage Demo
passwordDemo.keyLabel=Key:
passwordDemo.usernameLabel=Username:
passwordDemo.passwordLabel=Password:
passwordDemo.storeButton=Store Password
passwordDemo.retrieveButton=Retrieve Password
passwordDemo.removeButton=Remove Password
passwordDemo.testButton=Test with Demo Data
passwordDemo.clearButton=Clear Results
passwordDemo.resultLabel=Result:
passwordDemo.statusLabel=Status:
passwordDemo.success=Success
passwordDemo.failed=Failed
passwordDemo.notFound=Not found
passwordDemo.stored=Password stored successfully
passwordDemo.retrieved=Password retrieved: {0}
passwordDemo.removed=Password removed successfully
passwordDemo.error=Error: {0}
