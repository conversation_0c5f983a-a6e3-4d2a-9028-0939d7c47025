<!-- Plugin Configuration File. Read more: https://plugins.jetbrains.com/docs/intellij/plugin-configuration-file.html -->
<idea-plugin>
    <id>com.github.emprider.intellijplugin</id>
    <name>Password Storage Demo Plugin</name>
    <vendor email="<EMAIL>" url="https://github.com/emprider">emprider</vendor>

    <description><![CDATA[
    A demonstration plugin showing how to securely store and retrieve passwords using IntelliJ's PasswordSafe API.

    Features:
    - Secure password storage using OS-level credential storage
    - Easy-to-use tool window interface
    - Support for Windows (KeePass), macOS (Keychain), and Linux (Secret Service)
    - Test functionality with demo data
    - Comprehensive error handling and logging
    ]]></description>

    <depends>com.intellij.modules.platform</depends>

    <resource-bundle>messages.MyBundle</resource-bundle>

    <extensions defaultExtensionNs="com.intellij">
        <toolWindow factoryClass="com.github.emprider.intellijplugin.toolWindow.MyToolWindowFactory" id="PasswordDemo" displayName="Password Storage Demo" anchor="right"/>
        <postStartupActivity implementation="com.github.emprider.intellijplugin.startup.MyProjectActivity" />
    </extensions>
</idea-plugin>
