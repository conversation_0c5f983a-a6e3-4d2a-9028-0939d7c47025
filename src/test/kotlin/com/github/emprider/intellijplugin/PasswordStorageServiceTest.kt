package com.github.emprider.intellijplugin

import com.intellij.openapi.components.service
import com.intellij.testFramework.fixtures.BasePlatformTestCase
import com.github.emprider.intellijplugin.services.PasswordStorageService

/**
 * Test class for PasswordStorageService to verify password storage functionality.
 * 
 * These tests demonstrate how IntelliJ's PasswordSafe API works in a testing environment
 * and verify that our service correctly handles password storage, retrieval, and removal.
 */
class PasswordStorageServiceTest : BasePlatformTestCase() {

    private lateinit var passwordService: PasswordStorageService

    override fun setUp() {
        super.setUp()
        passwordService = project.service<PasswordStorageService>()
    }

    fun testStoreAndRetrievePassword() {
        val key = "test-key-1"
        val username = "test-user"
        val password = "test-password-123"

        // Store password
        val storeResult = passwordService.storePassword(key, username, password)
        assertTrue("Password should be stored successfully", storeResult)

        // Retrieve password
        val retrievedCredentials = passwordService.retrieveCredentials(key)
        assertNotNull("Credentials should be retrieved", retrievedCredentials)
        
        val (retrievedUsername, retrievedPassword) = retrievedCredentials!!
        assertEquals("Username should match", username, retrievedUsername)
        assertEquals("Password should match", password, retrievedPassword)

        // Clean up
        passwordService.removePassword(key)
    }

    fun testRetrievePasswordOnly() {
        val key = "test-key-2"
        val username = "test-user-2"
        val password = "test-password-456"

        // Store password
        passwordService.storePassword(key, username, password)

        // Retrieve password only
        val retrievedPassword = passwordService.retrievePassword(key)
        assertNotNull("Password should be retrieved", retrievedPassword)
        assertEquals("Password should match", password, retrievedPassword)

        // Clean up
        passwordService.removePassword(key)
    }

    fun testRemovePassword() {
        val key = "test-key-3"
        val username = "test-user-3"
        val password = "test-password-789"

        // Store password
        passwordService.storePassword(key, username, password)

        // Verify it exists
        val credentials = passwordService.retrieveCredentials(key)
        assertNotNull("Credentials should exist before removal", credentials)

        // Remove password
        val removeResult = passwordService.removePassword(key)
        assertTrue("Password should be removed successfully", removeResult)

        // Verify it's gone
        val credentialsAfterRemoval = passwordService.retrieveCredentials(key)
        assertNull("Credentials should not exist after removal", credentialsAfterRemoval)
    }

    fun testRetrieveNonExistentPassword() {
        val key = "non-existent-key"

        // Try to retrieve non-existent password
        val credentials = passwordService.retrieveCredentials(key)
        assertNull("Non-existent credentials should return null", credentials)

        val password = passwordService.retrievePassword(key)
        assertNull("Non-existent password should return null", password)
    }

    fun testTestPasswordFunctionality() {
        // Test storing test password
        val storeResult = passwordService.storeTestPassword()
        assertTrue("Test password should be stored successfully", storeResult)

        // Test checking if test credentials exist
        val hasCredentials = passwordService.hasTestCredentials()
        assertTrue("Test credentials should exist", hasCredentials)

        // Test retrieving test credentials
        val testCredentials = passwordService.retrieveTestCredentials()
        assertNotNull("Test credentials should be retrieved", testCredentials)

        val (username, password) = testCredentials!!
        assertEquals("Test username should match", "demo-user", username)
        assertEquals("Test password should match", "demo-password-123", password)

        // Test removing test password
        val removeResult = passwordService.removeTestPassword()
        assertTrue("Test password should be removed successfully", removeResult)

        // Verify it's gone
        val hasCredentialsAfterRemoval = passwordService.hasTestCredentials()
        assertFalse("Test credentials should not exist after removal", hasCredentialsAfterRemoval)
    }

    fun testEmptyUsernameHandling() {
        val key = "test-key-empty-username"
        val username = ""
        val password = "test-password"

        // Store password with empty username
        val storeResult = passwordService.storePassword(key, username, password)
        assertTrue("Password should be stored even with empty username", storeResult)

        // Retrieve and verify
        val credentials = passwordService.retrieveCredentials(key)
        assertNotNull("Credentials should be retrieved", credentials)
        
        val (retrievedUsername, retrievedPassword) = credentials!!
        assertEquals("Empty username should be preserved", username, retrievedUsername)
        assertEquals("Password should match", password, retrievedPassword)

        // Clean up
        passwordService.removePassword(key)
    }

    fun testMultiplePasswordsWithDifferentKeys() {
        val keys = listOf("key1", "key2", "key3")
        val credentials = listOf(
            Pair("user1", "pass1"),
            Pair("user2", "pass2"),
            Pair("user3", "pass3")
        )

        // Store multiple passwords
        keys.forEachIndexed { index, key ->
            val (username, password) = credentials[index]
            val result = passwordService.storePassword(key, username, password)
            assertTrue("Password $index should be stored", result)
        }

        // Retrieve and verify all passwords
        keys.forEachIndexed { index, key ->
            val retrievedCredentials = passwordService.retrieveCredentials(key)
            assertNotNull("Credentials $index should be retrieved", retrievedCredentials)
            
            val (expectedUsername, expectedPassword) = credentials[index]
            val (actualUsername, actualPassword) = retrievedCredentials!!
            
            assertEquals("Username $index should match", expectedUsername, actualUsername)
            assertEquals("Password $index should match", expectedPassword, actualPassword)
        }

        // Clean up all passwords
        keys.forEach { key ->
            passwordService.removePassword(key)
        }
    }
}
